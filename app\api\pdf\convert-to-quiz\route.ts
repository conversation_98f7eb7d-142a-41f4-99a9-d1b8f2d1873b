import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    // 文件验证
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Unsupported file type' },
        { status: 400 }
      );
    }
    
    if (file.size > 20 * 1024 * 1024) { // 20MB
      return NextResponse.json(
        { error: 'File size exceeds 20MB limit' },
        { status: 400 }
      );
    }
    
    // 这里应该实现实际的PDF处理和Quiz生成逻辑
    // 暂时返回模拟数据
    const mockQuiz = {
      id: 'quiz_' + Date.now(),
      title: `Quiz from ${file.name}`,
      questions: [
        {
          id: 'q1',
          text: 'What is the main topic of this document?',
          type: 'mcq',
          options: [
            { id: 'a', text: 'Option A', isCorrect: true },
            { id: 'b', text: 'Option B', isCorrect: false },
            { id: 'c', text: 'Option C', isCorrect: false },
            { id: 'd', text: 'Option D', isCorrect: false }
          ]
        }
      ]
    };
    
    return NextResponse.json({
      success: true,
      quiz: mockQuiz
    });
    
  } catch (error) {
    console.error('Quiz generation error:', error);
    return NextResponse.json(
      { error: 'Failed to process file' },
      { status: 500 }
    );
  }
}
