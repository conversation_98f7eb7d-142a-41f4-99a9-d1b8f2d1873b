'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from 'sonner';

interface FileUploaderProps {
  onFileUpload?: (file: File) => void;
  onQuizGenerated?: (quiz: any) => void;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onFileUpload,
  onQuizGenerated
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  }, []);

  const handleFileUpload = async (file: File) => {
    // 文件验证
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/heic',
      'application/vnd.oasis.opendocument.presentation',
      'application/vnd.oasis.opendocument.text',
      'image/bmp',
      'image/tiff'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a supported file format');
      return;
    }

    if (file.size > 20 * 1024 * 1024) { // 20MB limit
      toast.error('File size must be less than 20MB');
      return;
    }

    setUploadedFile(file);
    if (onFileUpload) {
      onFileUpload(file);
    }
    toast.success('File uploaded successfully!');
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  };

  const convertToQuiz = async () => {
    if (!uploadedFile) {
      toast.error('Please upload a file first');
      return;
    }

    setIsGenerating(true);
    
    try {
      const formData = new FormData();
      formData.append('file', uploadedFile);
      
      // 这里需要实际的API调用
      // const response = await fetch('/api/pdf/convert-to-quiz', {
      //   method: 'POST',
      //   body: formData,
      // });
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // const result = await response.json();
      // if (onQuizGenerated) {
      //   onQuizGenerated(result.quiz);
      // }
      
      toast.success('Quiz generated successfully!');
    } catch (error) {
      toast.error('Failed to generate quiz. Please try again.');
      console.error('Quiz generation error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto space-y-6">
      {/* 文件上传区域 */}
      <Card 
        className={`p-8 border-2 border-dashed transition-colors ${
          dragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-primary/50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="text-center space-y-4">
          {/* PDF图标 */}
          <div className="flex justify-center items-center space-x-4">
            <div className="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-4xl">→</div>
            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              {uploadedFile ? uploadedFile.name : 'Click to upload or drag and drop'}
            </h3>
            <p className="text-gray-500 mt-2">
              PDF, DOCX, PPTX, TXT, JPG, JPEG, PNG, HEIC, ODP, ODT, BMP, or TIFF
            </p>
            <p className="text-gray-500 text-sm">up to 20MB</p>
          </div>

          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileInputChange}
            accept=".pdf,.docx,.pptx,.txt,.jpg,.jpeg,.png,.heic,.odp,.odt,.bmp,.tiff"
          />
          
          <Button 
            onClick={() => document.getElementById('file-upload')?.click()}
            variant="outline"
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : '选择文件'}
          </Button>
        </div>
      </Card>

      {/* 转换按钮 */}
      {uploadedFile && (
        <div className="text-center">
          <Button 
            onClick={convertToQuiz}
            disabled={isGenerating}
            className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg"
            size="lg"
          >
            {isGenerating ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Please wait, your quiz is being created...</span>
              </div>
            ) : (
              <>Convert to Quiz →</>
            )}
          </Button>
        </div>
      )}

      {/* 安全提示 */}
      <p className="text-sm text-gray-500 text-center">
        🔒 Your PDFs will be safely handled by our servers and deleted after the quiz creation.
      </p>
    </div>
  );
};
