import { NextRequest, NextResponse } from 'next/server';
import { CreemService } from '@/services/creem';
import { respErr, respData } from '@/lib/resp';

export async function POST(req: NextRequest) {
  try {
    const { customer_id, customer_email } = await req.json();

    if (!customer_id && !customer_email) {
      return respErr("customer_id or customer_email is required");
    }

    const creemService = CreemService.getInstance();

    let customerId = customer_id;

    // 如果只有邮箱，先获取客户ID
    if (!customerId && customer_email) {
      try {
        const customer = await creemService.getCustomer(customer_email);
        customerId = customer.id;
      } catch (error) {
        console.error("Failed to get customer by email:", error);
        return respErr("Customer not found");
      }
    }

    if (!customerId) {
      return respErr("Unable to determine customer ID");
    }

    // 创建客户门户会话
    const portalSession = await creemService.createCustomerPortalSession(customerId);

    return respData({
      portal_url: portalSession.customer_portal_link,
    });

  } catch (e: any) {
    console.error("Creem portal creation failed:", e);
    return respErr("Portal creation failed: " + e.message);
  }
}
