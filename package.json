{"name": "ship-template-one", "version": "1.6.0", "private": true, "author": " ", "homepage": " ", "scripts": {"dev": "cross-env NODE_NO_WARNINGS=1 next dev --turbopack", "build": "next build", "start": "NODE_NO_WARNINGS=1 next start", "lint": "next lint", "analyze": "ANALYZE=true pnpm build", "cf:build": "npx @cloudflare/next-on-pages", "cf:preview": "pnpm cf:build && wrangler pages dev", "cf:deploy": "pnpm cf:build && wrangler pages deploy", "docker:build": "docker build -f Dockerfile -t ship-template-one:latest ."}, "dependencies": {"@ai-sdk/deepseek": "^0.1.11", "@ai-sdk/openai": "^1.1.13", "@ai-sdk/openai-compatible": "^0.0.17", "@ai-sdk/provider": "^1.0.8", "@ai-sdk/provider-utils": "^2.0.7", "@ai-sdk/replicate": "^0.1.10", "@aws-sdk/client-s3": "^3.740.0", "@aws-sdk/lib-storage": "^3.740.0", "@devnomic/marquee": "^1.0.2", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.3", "@next/third-parties": "^15.1.2", "@openpanel/nextjs": "^1.0.7", "@openrouter/ai-sdk-provider": "^0.0.6", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "^5.4.0", "@supabase/supabase-js": "^2.47.10", "@types/canvas-confetti": "^1.9.0", "@types/mdx": "^2.0.13", "@uiw/react-md-editor": "^4.0.5", "ai": "^4.1.64", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-fade": "^8.5.1", "embla-carousel-react": "^8.5.1", "eslint-config-next": "^15.2.3", "framer-motion": "^11.15.0", "google-one-tap": "^1.0.6", "highlight.js": "^11.11.0", "lucide-react": "^0.439.0", "markdown-it": "^14.1.0", "moment": "^2.30.1", "next": "15.2.3", "next-auth": "5.0.0-beta.25", "next-intl": "^4.0.2", "next-themes": "^0.4.4", "openai": "^4.78.1", "react": "^19.0.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icon-cloud": "^4.1.4", "react-icons": "^5.4.0", "react-tweet": "^3.2.1", "simple-flakeid": "^0.0.5", "sonner": "^1.7.1", "stripe": "^17.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1", "react-dropzone": "^14.0.0"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.7", "@next/bundle-analyzer": "^15.1.3", "@types/markdown-it": "^14.1.2", "@types/node": "^20.17.10", "@types/react": "^18.3.18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vercel": "39.1.1", "wrangler": "3.97.0"}}